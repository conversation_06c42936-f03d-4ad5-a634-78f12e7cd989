#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仓位上报客户端Demo
提供完整的错误处理，确保不影响主程序运行
"""

import requests
import json
import time
from datetime import datetime
from typing import Optional, Dict, Any


class PositionReportClient:
    """仓位上报客户端 - 带完整错误处理"""

    def __init__(self, base_url: str = "http://**************:5000", timeout: int = 5):
        """
        初始化客户端

        Args:
            base_url: 服务器地址
            timeout: 请求超时时间(秒)
        """
        self.base_url = base_url.rstrip('/')
        self.api_url = f"{self.base_url}/api/position"
        self.timeout = timeout
        self.session = requests.Session()

    def _safe_request(self, data: Dict[str, Any]) -> bool:
        """
        安全的请求发送，包含完整错误处理

        Args:
            data: 要发送的数据

        Returns:
            bool: 是否发送成功
        """
        try:
            # 数据验证
            if not isinstance(data, dict):
                self.logger.error("数据格式错误: 必须是字典类型")
                return False

            # 发送请求
            response = self.session.post(
                self.api_url,
                json=data,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )

            # 检查响应状态
            if response.status_code == 200:
                try:
                    result = response.json()
                    return True
                except json.JSONDecodeError:
                    return False
            else:
                return False

        except requests.exceptions.Timeout:
            return False
        except requests.exceptions.ConnectionError:
            return False
        except requests.exceptions.RequestException as e:
            return False
        except Exception as e:
            return False

    def open_position(self, exchange: str, symbol: str, position_side: str,
                     open_price: float, target_quantity: float,
                     current_price: Optional[float] = None) -> bool:
        """
        开始开仓

        Args:
            exchange: 交易所名称 (如: binance, okx, bybit)
            symbol: 交易对 (如: BTCUSDT, ETHUSDT)
            position_side: 仓位方向 (LONG 或 SHORT)
            open_price: 开仓价格
            target_quantity: 目标数量
            current_price: 当前价格 (可选，默认使用开仓价格)

        Returns:
            bool: 是否开仓成功
        """
        try:
            if current_price is None:
                current_price = open_price

            data = {
                "exchange": str(exchange),
                "symbol": str(symbol),
                "action": "open_pos",
                "position": {
                    "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "open_price": str(open_price),
                    "open_quantity": str(target_quantity),
                    "open_avg_price": str(open_price),
                    "current_quantity": "0",
                    "position_side": str(position_side).upper(),
                    "current_price": str(current_price)
                }
            }

            return self._safe_request(data)

        except Exception as e:
            return False

    def update_position(self, exchange: str, symbol: str, position_side: str,
                       open_price: float, target_quantity: float,
                       current_quantity: float, avg_price: Optional[float] = None,
                       current_price: Optional[float] = None) -> bool:
        """
        更新仓位

        Args:
            exchange: 交易所名称
            symbol: 交易对
            position_side: 仓位方向 (LONG 或 SHORT)
            open_price: 开仓价格
            target_quantity: 目标数量
            current_quantity: 当前数量
            avg_price: 平均价格 (可选，默认使用开仓价格)
            current_price: 当前价格 (可选，默认使用开仓价格)

        Returns:
            bool: 是否更新成功
        """
        try:
            if avg_price is None:
                avg_price = open_price
            if current_price is None:
                current_price = open_price

            data = {
                "exchange": str(exchange),
                "symbol": str(symbol),
                "action": "update_pos",
                "position": {
                    "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "open_price": str(open_price),
                    "open_quantity": str(target_quantity),
                    "open_avg_price": str(avg_price),
                    "current_quantity": str(current_quantity),
                    "position_side": str(position_side).upper(),
                    "current_price": str(current_price)
                }
            }

            return self._safe_request(data)

        except Exception as e:
            return False

    def report_long_position(self, exchange: str, symbol: str,
                           open_price: float, quantity: float,
                           avg_price: Optional[float] = None,
                           current_price: Optional[float] = None) -> bool:
        """
        上报多头(LONG)仓位

        Args:
            exchange: 交易所名称
            symbol: 交易对
            open_price: 开仓价格
            quantity: 仓位数量
            avg_price: 平均价格
            current_price: 当前价格

        Returns:
            bool: 是否上报成功
        """
        try:
            return self.update_position(
                exchange=exchange,
                symbol=symbol,
                position_side="LONG",
                open_price=open_price,
                target_quantity=quantity,
                current_quantity=quantity,
                avg_price=avg_price,
                current_price=current_price
            )
        except Exception as e:
            return False

    def report_short_position(self, exchange: str, symbol: str,
                            open_price: float, quantity: float,
                            avg_price: Optional[float] = None,
                            current_price: Optional[float] = None) -> bool:
        """
        上报空头(SHORT)仓位

        Args:
            exchange: 交易所名称
            symbol: 交易对
            open_price: 开仓价格
            quantity: 仓位数量
            avg_price: 平均价格
            current_price: 当前价格

        Returns:
            bool: 是否上报成功
        """
        try:
            return self.update_position(
                exchange=exchange,
                symbol=symbol,
                position_side="SHORT",
                open_price=open_price,
                target_quantity=quantity,
                current_quantity=quantity,
                avg_price=avg_price,
                current_price=current_price
            )
        except Exception as e:
            return False

    def get_all_positions(self) -> Optional[Dict]:
        """
        获取所有仓位数据

        Returns:
            Dict: 所有仓位数据，失败时返回None
        """
        try:
            response = self.session.get(f"{self.base_url}/api/positions", timeout=self.timeout)
            if response.status_code == 200:
                return response.json()
            else:
                return None
        except Exception as e:
            return None

    def clear_all_positions(self) -> bool:
        """
        清除所有仓位数据

        Returns:
            bool: 是否清除成功
        """
        try:
            response = self.session.post(
                f"{self.base_url}/api/positions/clear",
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.logger.info(f"清除成功: {result.get('message', '所有仓位数据已清除')}")
                    return True
                except json.JSONDecodeError:
                    self.logger.error("服务器响应格式错误")
                    return False
            else:
                self.logger.error(f"清除失败: {response.status_code}")
                return False

        except requests.exceptions.Timeout:
            self.logger.error(f"清除请求超时 (>{self.timeout}秒)")
            return False
        except requests.exceptions.ConnectionError:
            self.logger.error("清除失败: 无法连接到服务器")
            return False
        except requests.exceptions.RequestException as e:
            self.logger.error(f"清除请求异常: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"清除数据时发生未知错误: {str(e)}")
            return False


def simulate_gradual_opening(client, exchange: str, symbol: str, position_side: str,
                           open_price: float, target_quantity: float,
                           current_price: float = None):
    """
    模拟逐步开仓过程

    Args:
        client: PositionReportClient实例
        exchange: 交易所名称
        symbol: 交易对
        position_side: 仓位方向
        open_price: 开仓价格
        target_quantity: 目标数量
        current_price: 当前价格
    """
    if current_price is None:
        current_price = open_price

    print(f"\n🚀 开始模拟 {position_side} 仓位开仓过程")
    print(f"交易对: {exchange} {symbol}")
    print(f"目标数量: {target_quantity}")
    print("=" * 60)

    # 1. 开始开仓
    success = client.open_position(
        exchange=exchange,
        symbol=symbol,
        position_side=position_side,
        open_price=open_price,
        target_quantity=target_quantity,
        current_price=current_price
    )
    print(f"📍 开仓开始: {'成功' if success else '失败'}")

    if not success:
        print("❌ 开仓失败，停止模拟")
        return

    # 2. 模拟逐步开仓过程 (每秒更新一次，直到95%以上)
    steps = 20  # 分20步完成开仓
    for i in range(1, steps + 1):
        # 计算当前仓位进度
        progress = i / steps
        current_quantity = target_quantity * progress

        # 模拟价格波动
        price_variation = current_price * 0.001 * (i % 3 - 1)  # ±0.1%的价格波动
        current_market_price = current_price + price_variation

        # 更新仓位
        success = client.update_position(
            exchange=exchange,
            symbol=symbol,
            position_side=position_side,
            open_price=open_price,
            target_quantity=target_quantity,
            current_quantity=current_quantity,
            avg_price=open_price,
            current_price=current_market_price
        )

        progress_percent = progress * 100
        print(f"📊 步骤 {i:2d}/20: 仓位进度 {progress_percent:5.1f}% "
              f"({current_quantity:.3f}/{target_quantity}) "
              f"价格: {current_market_price:.2f} "
              f"{'✅' if success else '❌'}")

        # 如果达到95%以上，再更新几次然后停止
        if progress >= 0.95:
            print(f"🎯 已达到95%以上，再更新2次后停止")
            for j in range(2):
                time.sleep(1)
                # 继续小幅更新
                final_progress = min(1.0, progress + (j + 1) * 0.01)
                final_quantity = target_quantity * final_progress

                success = client.update_position(
                    exchange=exchange,
                    symbol=symbol,
                    position_side=position_side,
                    open_price=open_price,
                    target_quantity=target_quantity,
                    current_quantity=final_quantity,
                    avg_price=open_price,
                    current_price=current_market_price
                )

                final_percent = final_progress * 100
                print(f"📊 最终步骤 {j+1}: 仓位进度 {final_percent:5.1f}% "
                      f"({final_quantity:.3f}/{target_quantity}) "
                      f"{'✅' if success else '❌'}")
            break

        # 每秒更新一次
        time.sleep(1)

    print(f"✅ {position_side} 仓位开仓模拟完成！")


# 使用示例
if __name__ == "__main__":
    # 创建客户端
    client = PositionReportClient("http://**************:5000")

    print("🚀 仓位上报客户端Demo - 逐步开仓模拟")
    print("=" * 60)

    # 模拟LONG仓位逐步开仓
    simulate_gradual_opening(
        client=client,
        exchange="binance",
        symbol="BTCUSDT",
        position_side="LONG",
        open_price=50000,
        target_quantity=2.0,
        current_price=50000
    )

    print("\n" + "=" * 60)
    print("等待5秒后开始SHORT仓位模拟...")
    time.sleep(5)

    # 模拟SHORT仓位逐步开仓
    simulate_gradual_opening(
        client=client,
        exchange="binance",
        symbol="ETHUSDT",
        position_side="SHORT",
        open_price=3200,
        target_quantity=5.0,
        current_price=3200
    )

    print("\n✅ 所有模拟完成！访问 http://**************:5000 查看结果")
