#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仓位上报客户端Demo
提供完整的错误处理，确保不影响主程序运行
"""

import requests
import json
import time
from datetime import datetime
from typing import Optional, Dict, Any


class PositionReportClient:
    """仓位上报客户端 - 带完整错误处理"""

    def __init__(self, base_url: str = "http://**************:5000", timeout: int = 5):
        """
        初始化客户端

        Args:
            base_url: 服务器地址
            timeout: 请求超时时间(秒)
        """
        self.base_url = base_url.rstrip('/')
        self.api_url = f"{self.base_url}/api/position"
        self.timeout = timeout
        self.session = requests.Session()

    def _safe_request(self, data: Dict[str, Any]) -> bool:
        """
        安全的请求发送，包含完整错误处理

        Args:
            data: 要发送的数据

        Returns:
            bool: 是否发送成功
        """
        try:
            # 数据验证
            if not isinstance(data, dict):
                self.logger.error("数据格式错误: 必须是字典类型")
                return False

            # 发送请求
            response = self.session.post(
                self.api_url,
                json=data,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )

            # 检查响应状态
            if response.status_code == 200:
                try:
                    result = response.json()
                    return True
                except json.JSONDecodeError:
                    return False
            else:
                return False

        except requests.exceptions.Timeout:
            return False
        except requests.exceptions.ConnectionError:
            return False
        except requests.exceptions.RequestException as e:
            return False
        except Exception as e:
            return False

    def open_position(self, exchange: str, symbol: str, position_side: str,
                     open_price: float, target_quantity: float,
                     current_price: Optional[float] = None) -> bool:
        """
        开始开仓

        Args:
            exchange: 交易所名称 (如: binance, okx, bybit)
            symbol: 交易对 (如: BTCUSDT, ETHUSDT)
            position_side: 仓位方向 (LONG 或 SHORT)
            open_price: 开仓价格
            target_quantity: 目标数量
            current_price: 当前价格 (可选，默认使用开仓价格)

        Returns:
            bool: 是否开仓成功
        """
        try:
            if current_price is None:
                current_price = open_price

            data = {
                "exchange": str(exchange),
                "symbol": str(symbol),
                "action": "open_pos",
                "position": {
                    "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "open_price": str(open_price),
                    "open_quantity": str(target_quantity),
                    "open_avg_price": str(open_price),
                    "current_quantity": "0",
                    "position_side": str(position_side).upper(),
                    "current_price": str(current_price)
                }
            }

            return self._safe_request(data)

        except Exception as e:
            return False

    def update_position(self, exchange: str, symbol: str, position_side: str,
                       open_price: float, target_quantity: float,
                       current_quantity: float, avg_price: Optional[float] = None,
                       current_price: Optional[float] = None) -> bool:
        """
        更新仓位

        Args:
            exchange: 交易所名称
            symbol: 交易对
            position_side: 仓位方向 (LONG 或 SHORT)
            open_price: 开仓价格
            target_quantity: 目标数量
            current_quantity: 当前数量
            avg_price: 平均价格 (可选，默认使用开仓价格)
            current_price: 当前价格 (可选，默认使用开仓价格)

        Returns:
            bool: 是否更新成功
        """
        try:
            if avg_price is None:
                avg_price = open_price
            if current_price is None:
                current_price = open_price

            data = {
                "exchange": str(exchange),
                "symbol": str(symbol),
                "action": "update_pos",
                "position": {
                    "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "open_price": str(open_price),
                    "open_quantity": str(target_quantity),
                    "open_avg_price": str(avg_price),
                    "current_quantity": str(current_quantity),
                    "position_side": str(position_side).upper(),
                    "current_price": str(current_price)
                }
            }

            return self._safe_request(data)

        except Exception as e:
            return False

    def report_long_position(self, exchange: str, symbol: str,
                           open_price: float, quantity: float,
                           avg_price: Optional[float] = None,
                           current_price: Optional[float] = None) -> bool:
        """
        上报多头(LONG)仓位

        Args:
            exchange: 交易所名称
            symbol: 交易对
            open_price: 开仓价格
            quantity: 仓位数量
            avg_price: 平均价格
            current_price: 当前价格

        Returns:
            bool: 是否上报成功
        """
        try:
            return self.update_position(
                exchange=exchange,
                symbol=symbol,
                position_side="LONG",
                open_price=open_price,
                target_quantity=quantity,
                current_quantity=quantity,
                avg_price=avg_price,
                current_price=current_price
            )
        except Exception as e:
            return False

    def report_short_position(self, exchange: str, symbol: str,
                            open_price: float, quantity: float,
                            avg_price: Optional[float] = None,
                            current_price: Optional[float] = None) -> bool:
        """
        上报空头(SHORT)仓位

        Args:
            exchange: 交易所名称
            symbol: 交易对
            open_price: 开仓价格
            quantity: 仓位数量
            avg_price: 平均价格
            current_price: 当前价格

        Returns:
            bool: 是否上报成功
        """
        try:
            return self.update_position(
                exchange=exchange,
                symbol=symbol,
                position_side="SHORT",
                open_price=open_price,
                target_quantity=quantity,
                current_quantity=quantity,
                avg_price=avg_price,
                current_price=current_price
            )
        except Exception as e:
            return False

    def get_all_positions(self) -> Optional[Dict]:
        """
        获取所有仓位数据

        Returns:
            Dict: 所有仓位数据，失败时返回None
        """
        try:
            response = self.session.get(f"{self.base_url}/api/positions", timeout=self.timeout)
            if response.status_code == 200:
                return response.json()
            else:
                return None
        except Exception as e:
            return None

    def clear_all_positions(self) -> bool:
        """
        清除所有仓位数据

        Returns:
            bool: 是否清除成功
        """
        try:
            response = self.session.post(
                f"{self.base_url}/api/positions/clear",
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.logger.info(f"清除成功: {result.get('message', '所有仓位数据已清除')}")
                    return True
                except json.JSONDecodeError:
                    self.logger.error("服务器响应格式错误")
                    return False
            else:
                self.logger.error(f"清除失败: {response.status_code}")
                return False

        except requests.exceptions.Timeout:
            self.logger.error(f"清除请求超时 (>{self.timeout}秒)")
            return False
        except requests.exceptions.ConnectionError:
            self.logger.error("清除失败: 无法连接到服务器")
            return False
        except requests.exceptions.RequestException as e:
            self.logger.error(f"清除请求异常: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"清除数据时发生未知错误: {str(e)}")
            return False


# 使用示例
if __name__ == "__main__":
    # 创建客户端
    client = PositionReportClient("http://**************:5000")

    print("🚀 仓位上报客户端Demo")
    print("=" * 50)

    # 开始开仓
    success = client.open_position(
        exchange="binance",
        symbol="BTCUSDT",
        position_side="LONG",
        open_price=49000,
        target_quantity=2.0
    )
    print(f"开仓开始: {'成功' if success else '失败'}")

    time.sleep(5)

    # 示例1: 上报LONG仓位
    print("1️⃣ 上报LONG仓位")
    success = client.report_long_position(
        exchange="binance",
        symbol="BTCUSDT",
        open_price=50000,
        quantity=1.0,
        current_price=51000
    )
    print(f"结果: {'成功' if success else '失败'}")

    time.sleep(5)

    # 示例1: 上报LONG仓位
    print("1️⃣ 上报LONG仓位")
    success = client.report_long_position(
        exchange="binance",
        symbol="BTCUSDT",
        open_price=50000,
        quantity=2.0,
        current_price=51500
    )
    print(f"结果: {'成功' if success else '失败'}")

    time.sleep(5)

    success = client.open_position(
        exchange="binance",
        symbol="BTCUSDT",
        position_side="SHORT",
        open_price=49000,
        target_quantity=2.0
    )
    print(f"开仓开始: {'成功' if success else '失败'}")

    time.sleep(5)

    # 示例2: 上报SHORT仓位
    print("\n2️⃣ 上报SHORT仓位")
    success = client.report_short_position(
        exchange="binance",
        symbol="BTCUSDT",
        open_price=3200,
        quantity=1.0,
        current_price=3150
    )
    print(f"结果: {'成功' if success else '失败'}")

    time.sleep(5)

    # 示例2: 上报SHORT仓位
    print("\n2️⃣ 上报SHORT仓位")
    success = client.report_short_position(
        exchange="binance",
        symbol="BTCUSDT",
        open_price=3200,
        quantity=2.0,
        current_price=3150
    )
    print(f"结果: {'成功' if success else '失败'}")

    print("\n✅ Demo完成！访问 http://**************:5000 查看结果")
