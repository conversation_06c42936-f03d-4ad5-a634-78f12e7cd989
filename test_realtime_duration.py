#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试开仓耗时和清仓耗时的实时变化
"""

import requests
import json
import time
from datetime import datetime
from position_client_demo import PositionReportClient


def test_realtime_opening_duration():
    """测试开仓耗时的实时变化"""
    client = PositionReportClient("http://18.183.131.174:5000")
    
    print("🚀 测试开仓耗时实时变化")
    print("=" * 50)
    
    # 1. 开始开仓
    success = client.open_position(
        exchange="binance",
        symbol="BTCUSDT",
        position_side="LONG",
        open_price=50000,
        target_quantity=2.0
    )
    print(f"📍 开仓开始: {'成功' if success else '失败'}")
    
    if not success:
        return
    
    # 2. 逐步更新仓位，观察开仓耗时变化
    for i in range(1, 11):  # 10步，每步10%
        progress = i * 0.1
        current_quantity = 2.0 * progress
        
        # 更新仓位
        success = client.update_position(
            exchange="binance",
            symbol="BTCUSDT",
            position_side="LONG",
            open_price=50000,
            target_quantity=2.0,
            current_quantity=current_quantity,
            avg_price=50000,
            current_price=50000
        )
        
        # 获取当前数据
        try:
            response = requests.get("http://18.183.131.174:5000/api/positions", timeout=5)
            if response.status_code == 200:
                data = response.json()
                position_data = data.get("binance", {}).get("BTCUSDT", {}).get("LONG", {})
                opening_duration = position_data.get("opening_duration", 0)
                progress_percent = position_data.get("progress", 0)
                
                print(f"📊 步骤 {i:2d}: 仓位 {progress_percent:5.1f}% "
                      f"开仓耗时: {opening_duration:2d}秒 "
                      f"({'✅' if success else '❌'})")
            else:
                print(f"📊 步骤 {i:2d}: 获取数据失败")
        except:
            print(f"📊 步骤 {i:2d}: 网络错误")
        
        time.sleep(2)  # 每2秒更新一次，便于观察
    
    print("✅ 开仓耗时测试完成")


def test_realtime_closing_duration():
    """测试清仓耗时的实时变化"""
    client = PositionReportClient("http://18.183.131.174:5000")
    
    print("\n🔥 测试清仓耗时实时变化")
    print("=" * 50)
    
    # 先确保仓位在95%以上
    print("💎 设置仓位到98%...")
    success = client.update_position(
        exchange="binance",
        symbol="BTCUSDT",
        position_side="LONG",
        open_price=50000,
        target_quantity=2.0,
        current_quantity=1.96,  # 98%
        avg_price=50000,
        current_price=50000
    )
    time.sleep(2)
    
    # 开始清仓过程
    print("🔥 开始清仓过程...")
    for i in range(1, 11):  # 从90%降到5%
        progress = 0.9 - (i * 0.085)  # 从90%降到5%
        progress = max(0.05, progress)  # 最低5%
        current_quantity = 2.0 * progress
        
        # 更新仓位
        success = client.update_position(
            exchange="binance",
            symbol="BTCUSDT",
            position_side="LONG",
            open_price=50000,
            target_quantity=2.0,
            current_quantity=current_quantity,
            avg_price=50000,
            current_price=50000
        )
        
        # 获取当前数据
        try:
            response = requests.get("http://18.183.131.174:5000/api/positions", timeout=5)
            if response.status_code == 200:
                data = response.json()
                position_data = data.get("binance", {}).get("BTCUSDT", {}).get("LONG", {})
                closing_duration = position_data.get("closing_duration", 0)
                progress_percent = position_data.get("progress", 0)
                
                print(f"🔥 步骤 {i:2d}: 仓位 {progress_percent:5.1f}% "
                      f"清仓耗时: {closing_duration:2d}秒 "
                      f"({'✅' if success else '❌'})")
            else:
                print(f"🔥 步骤 {i:2d}: 获取数据失败")
        except:
            print(f"🔥 步骤 {i:2d}: 网络错误")
        
        time.sleep(2)  # 每2秒更新一次，便于观察
    
    print("✅ 清仓耗时测试完成")


if __name__ == "__main__":
    # 清除所有仓位
    try:
        response = requests.post("http://18.183.131.174:5000/api/positions/clear", timeout=5)
        print("🧹 清除所有仓位")
    except:
        print("🧹 清除失败，继续测试")
    
    time.sleep(1)
    
    # 测试开仓耗时实时变化
    test_realtime_opening_duration()
    
    # 测试清仓耗时实时变化
    test_realtime_closing_duration()
    
    print("\n✅ 所有测试完成！访问 http://18.183.131.174:5000 查看页面实时变化")
