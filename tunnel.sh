#!/bin/bash

# SSH隧道脚本 - 用于通过SSH访问远程服务
# 使用方法: ./tunnel.sh [local_port]

LOCAL_PORT=${1:-8080}
REMOTE_HOST="live-analyze"
REMOTE_PORT=5000

echo "🚇 创建SSH隧道..."
echo "本地端口: $LOCAL_PORT"
echo "远程服务: $REMOTE_HOST:$REMOTE_PORT"
echo ""
echo "访问地址: http://localhost:$LOCAL_PORT"
echo ""
echo "按 Ctrl+C 停止隧道"
echo "=================================="

# 创建SSH隧道
ssh -L $LOCAL_PORT:localhost:$REMOTE_PORT $REMOTE_HOST -N
