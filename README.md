# 多交易所持仓可视化项目

## 功能特性
- 实时接收多个交易所的持仓数据
- 可视化展示 LONG/SHORT 仓位进度条
- 根据盈亏情况显示不同颜色（绿色亏损，红色盈利）
- 显示开仓时间和持仓时间
- 数据持久化到日志文件

## 快速开始

### 本地运行

#### 1. 安装依赖
```bash
pip install -r requirements.txt
```

#### 2. 运行服务器
```bash
python app.py
```

#### 3. 访问页面
打开浏览器访问：http://localhost:5000

#### 4. 上报数据
向 `http://localhost:5000/api/position` 发送 POST 请求：

```json
{
    "exchange": "binance",
    "symbol": "BTCUSDT",
    "action": "update_pos",
    "position": {
        "time": "2020-01-01 00:00:00",
        "open_price": "50000",
        "open_quantity": "1.0",
        "open_avg_price": "50100",
        "current_quantity": "0.8",
        "position_side": "LONG"
    }
}
```

### 🚀 一键部署到服务器

```bash
# 部署到服务器
./deploy.sh live-analyze 5000

# 服务管理
./manage.sh live-analyze status    # 查看状态
./manage.sh live-analyze logs      # 查看日志
./manage.sh live-analyze restart   # 重启服务
```

详细部署说明请查看 [DEPLOY.md](DEPLOY.md)

## 项目结构
```
.
├── app.py              # 主应用文件
├── templates/
│   └── index.html      # 前端页面
├── static/
│   ├── style.css       # 样式文件
│   └── script.js       # 前端脚本
├── logs/               # 日志目录
├── test_client.py      # 测试客户端
├── requirements.txt    # 依赖包
├── deploy.sh           # 一键部署脚本
├── manage.sh           # 服务管理脚本
├── tunnel.sh           # SSH隧道脚本
├── DEPLOY.md           # 部署说明文档
└── README.md           # 项目说明文档
```
