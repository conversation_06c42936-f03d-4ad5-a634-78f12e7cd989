#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
from datetime import datetime

def send_position_update(base_url, exchange, symbol, action, side, current_quantity, open_quantity="1.0", open_price="50000"):
    """发送仓位更新"""
    api_url = f"{base_url}/api/position"
    data = {
        "exchange": exchange,
        "symbol": symbol,
        "action": action,
        "position": {
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "open_price": open_price,
            "open_quantity": open_quantity,
            "open_avg_price": open_price,
            "current_quantity": str(current_quantity),
            "position_side": side
        }
    }
    
    response = requests.post(api_url, json=data)
    return response.status_code == 200

def test_filter_demo():
    """演示筛选功能"""
    base_url = "http://172.31.43.182:5000"
    
    print("🎬 演示筛选功能")
    print("=" * 60)
    print("💡 请在浏览器中打开 http://172.31.43.182:5000 观察筛选效果")
    print("=" * 60)
    
    # 创建多个交易所和品种的数据
    test_data = [
        # 币安交易所
        ("Binance", "BTCUSDT", "LONG", 0.8),
        ("Binance", "ETHUSDT", "SHORT", 0.6),
        ("Binance", "BTCETH", "LONG", 0.9),
        ("Binance", "ADAUSDT", "LONG", 0.7),
        
        # 欧易交易所
        ("OKX", "BTCUSDT", "LONG", 0.95),
        ("OKX", "ETHUSDT", "LONG", 0.85),
        ("OKX", "SOLUSDT", "SHORT", 0.75),
        ("OKX", "BTCEUR", "LONG", 0.65),
        
        # 火币交易所
        ("Huobi", "BTCUSDT", "SHORT", 0.55),
        ("Huobi", "ETHBTC", "LONG", 0.45),
        ("Huobi", "DOGEUSDT", "LONG", 0.35),
        ("Huobi", "BTCJPY", "SHORT", 0.25),
        
        # 其他交易所
        ("Coinbase", "BTCUSD", "LONG", 0.88),
        ("Coinbase", "ETHUSD", "SHORT", 0.78),
        ("Kraken", "XBTUSD", "LONG", 0.68),
        ("Kraken", "ETHUSD", "LONG", 0.58),
    ]
    
    print("1️⃣ 创建多个交易所和品种的测试数据...")
    
    for i, (exchange, symbol, side, quantity) in enumerate(test_data):
        print(f"  创建 {exchange}/{symbol}/{side} 仓位: {quantity*100:.0f}%")
        
        # 开仓
        send_position_update(base_url, exchange, symbol, "open_pos", side, 0)
        time.sleep(0.1)
        
        # 更新到指定仓位
        send_position_update(base_url, exchange, symbol, "update_pos", side, quantity)
        time.sleep(0.2)
    
    print("\n✅ 测试数据创建完成！")
    print("\n📋 现在可以在浏览器中测试筛选功能:")
    print("=" * 60)
    print("🔍 交易所筛选测试:")
    print("  1. 选择 'Binance' - 应该只显示币安的4个品种")
    print("  2. 选择 'OKX' - 应该只显示欧易的4个品种")
    print("  3. 选择 'Huobi' - 应该只显示火币的4个品种")
    print("  4. 选择 '全部交易所' - 显示所有交易所")
    
    print("\n🔍 品种筛选测试:")
    print("  1. 输入 'BTC' - 应该显示所有包含BTC的品种:")
    print("     - BTCUSDT (Binance, OKX, Huobi)")
    print("     - BTCETH (Binance)")
    print("     - BTCEUR (OKX)")
    print("     - BTCJPY (Huobi)")
    print("     - BTCUSD (Coinbase)")
    print("     - ETHBTC (Huobi)")
    print("     - XBTUSD (Kraken)")
    
    print("\n  2. 输入 'ETH' - 应该显示所有包含ETH的品种:")
    print("     - ETHUSDT (Binance, OKX)")
    print("     - BTCETH (Binance)")
    print("     - ETHBTC (Huobi)")
    print("     - ETHUSD (Coinbase, Kraken)")
    
    print("\n  3. 输入 'USDT' - 应该显示所有以USDT结尾的品种")
    print("  4. 清除筛选 - 显示所有品种")
    
    print("\n🔄 组合筛选测试:")
    print("  1. 选择交易所 'Binance' + 输入 'BTC'")
    print("     - 应该只显示: BTCUSDT, BTCETH")
    print("  2. 选择交易所 'OKX' + 输入 'USD'")
    print("     - 应该只显示: BTCUSDT, ETHUSDT, SOLUSDT")
    
    print("\n💡 筛选特性:")
    print("  - 交易所筛选：精确匹配")
    print("  - 品种筛选：模糊匹配（不区分大小写）")
    print("  - 实时筛选：输入时立即生效")
    print("  - 组合筛选：两个条件同时生效")
    print("  - 无数据提示：筛选无结果时显示提示")
    
    print(f"\n🌐 请访问 {base_url} 开始测试筛选功能！")
    
    # 等待用户测试
    print("\n⏳ 等待30秒供您测试筛选功能...")
    for i in range(30):
        print(f"  剩余时间: {30-i} 秒", end='\r')
        time.sleep(1)
    
    print("\n\n✅ 筛选功能演示完成！")

if __name__ == "__main__":
    test_filter_demo()
