#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
from datetime import datetime

def send_position_update(base_url, exchange, symbol, action, side, current_quantity, open_quantity="1.0", open_price="50000"):
    """发送仓位更新"""
    api_url = f"{base_url}/api/position"
    data = {
        "exchange": exchange,
        "symbol": symbol,
        "action": action,
        "position": {
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "open_price": open_price,
            "open_quantity": open_quantity,
            "open_avg_price": open_price,
            "current_quantity": str(current_quantity),
            "position_side": side
        }
    }
    
    response = requests.post(api_url, json=data)
    return response.status_code == 200

def get_position_data(base_url, exchange, symbol, side):
    """获取仓位数据"""
    try:
        response = requests.get(f"{base_url}/api/positions")
        if response.status_code == 200:
            data = response.json()
            if exchange in data and symbol in data[exchange] and side in data[exchange][symbol]:
                return data[exchange][symbol][side]
    except:
        pass
    return None

def print_position_summary(position, title):
    """打印仓位摘要"""
    if not position:
        print(f"{title}: 无数据")
        return
    
    print(f"{title}:")
    print(f"  开仓耗时: {position.get('opening_duration', 0)} 秒")
    print(f"  持仓时间: {position.get('holding_duration', 0)} 秒") 
    print(f"  清仓耗时: {position.get('closing_duration', 0)} 秒")
    print(f"  当前进度: {position.get('progress', 0):.1f}%")
    print(f"  开仓开始: {position.get('open_start_time', 'N/A')}")
    print(f"  达到95%: {position.get('position_95_time', 'N/A')}")
    print(f"  降到95%以下: {position.get('position_below_95_time', 'N/A')}")
    print(f"  降到5%: {position.get('position_5_time', 'N/A')}")

def test_comprehensive():
    """全面测试时间跟踪功能"""
    base_url = "http://172.31.43.182:5000"
    
    print("🧪 全面测试时间跟踪功能")
    print("=" * 80)
    print("📋 测试场景:")
    print("  1. 多次open_pos应该重置仓位")
    print("  2. 变更symbol应该重置仓位") 
    print("  3. LONG和SHORT都要测试")
    print("  4. 各种边界情况测试")
    print("=" * 80)
    
    # 测试1: 基本LONG仓位流程
    print("\n🔵 测试1: 基本LONG仓位流程")
    print("-" * 40)
    
    exchange = "test_exchange"
    symbol = "BTCUSDT"
    side = "LONG"
    
    # 开仓
    print("开始开仓...")
    send_position_update(base_url, exchange, symbol, "open_pos", side, 0)
    time.sleep(1)
    
    # 建仓到95%
    print("建仓到95%...")
    for i in range(1, 11):
        quantity = 0.1 * i
        send_position_update(base_url, exchange, symbol, "update_pos", side, quantity)
        if quantity >= 0.95:
            print(f"  达到95%: {quantity}")
            break
        time.sleep(0.5)
    
    # 持仓3秒
    print("持仓3秒...")
    time.sleep(3)
    
    # 减仓到5%以下
    print("减仓到5%以下...")
    for i in range(9, -1, -1):
        quantity = 0.1 * i
        send_position_update(base_url, exchange, symbol, "update_pos", side, quantity)
        if quantity < 0.95:
            print(f"  降到95%以下: {quantity}")
        if quantity <= 0.05:
            print(f"  降到5%以下: {quantity}")
            break
        time.sleep(0.5)
    
    time.sleep(1)
    position1 = get_position_data(base_url, exchange, symbol, side)
    print_position_summary(position1, "测试1结果")
    
    # 测试2: 多次open_pos应该重置
    print("\n🔄 测试2: 多次open_pos应该重置仓位")
    print("-" * 40)
    
    # 第一次开仓
    print("第一次开仓...")
    send_position_update(base_url, exchange, symbol, "open_pos", side, 0)
    time.sleep(1)
    send_position_update(base_url, exchange, symbol, "update_pos", side, 0.5)
    time.sleep(1)
    
    position_before = get_position_data(base_url, exchange, symbol, side)
    print(f"第一次开仓后开始时间: {position_before.get('open_start_time', 'N/A')}")
    
    # 第二次开仓（应该重置）
    print("第二次开仓（应该重置）...")
    time.sleep(2)  # 等待2秒确保时间不同
    send_position_update(base_url, exchange, symbol, "open_pos", side, 0)
    time.sleep(1)
    
    position_after = get_position_data(base_url, exchange, symbol, side)
    print(f"第二次开仓后开始时间: {position_after.get('open_start_time', 'N/A')}")
    
    if position_before.get('open_start_time') != position_after.get('open_start_time'):
        print("✅ 多次open_pos正确重置了时间")
    else:
        print("❌ 多次open_pos没有重置时间")
    
    # 测试3: SHORT仓位流程
    print("\n🔴 测试3: SHORT仓位流程")
    print("-" * 40)
    
    side = "SHORT"
    
    # 开仓
    print("SHORT开始开仓...")
    send_position_update(base_url, exchange, symbol, "open_pos", side, 0)
    time.sleep(1)
    
    # 建仓到95%
    print("SHORT建仓到95%...")
    for i in range(1, 11):
        quantity = 0.1 * i
        send_position_update(base_url, exchange, symbol, "update_pos", side, quantity)
        if quantity >= 0.95:
            print(f"  SHORT达到95%: {quantity}")
            break
        time.sleep(0.5)
    
    # 持仓2秒
    print("SHORT持仓2秒...")
    time.sleep(2)
    
    # 减仓到5%以下
    print("SHORT减仓到5%以下...")
    for i in range(9, -1, -1):
        quantity = 0.1 * i
        send_position_update(base_url, exchange, symbol, "update_pos", side, quantity)
        if quantity < 0.95:
            print(f"  SHORT降到95%以下: {quantity}")
        if quantity <= 0.05:
            print(f"  SHORT降到5%以下: {quantity}")
            break
        time.sleep(0.5)
    
    time.sleep(1)
    position3 = get_position_data(base_url, exchange, symbol, side)
    print_position_summary(position3, "测试3结果(SHORT)")
    
    # 测试4: 变更symbol应该独立
    print("\n🔀 测试4: 变更symbol应该独立")
    print("-" * 40)
    
    symbol2 = "ETHUSDT"
    side = "LONG"
    
    print("新symbol开仓...")
    send_position_update(base_url, exchange, symbol2, "open_pos", side, 0, "2.0", "3000")
    time.sleep(1)
    send_position_update(base_url, exchange, symbol2, "update_pos", side, 1.9, "2.0", "3000")
    time.sleep(1)
    
    position4 = get_position_data(base_url, exchange, symbol2, side)
    position_old = get_position_data(base_url, exchange, symbol, "SHORT")
    
    print(f"新symbol({symbol2})开始时间: {position4.get('open_start_time', 'N/A')}")
    print(f"旧symbol({symbol})SHORT时间: {position_old.get('open_start_time', 'N/A')}")
    
    if position4.get('open_start_time') != position_old.get('open_start_time'):
        print("✅ 不同symbol独立管理时间")
    else:
        print("❌ 不同symbol时间冲突")
    
    # 测试5: 边界情况 - 直接从0%到100%
    print("\n⚡ 测试5: 边界情况 - 直接跳跃")
    print("-" * 40)
    
    symbol3 = "ADAUSDT"
    side = "LONG"
    
    print("直接从0%跳到100%...")
    send_position_update(base_url, exchange, symbol3, "open_pos", side, 0)
    time.sleep(1)
    send_position_update(base_url, exchange, symbol3, "update_pos", side, 1.0)  # 直接100%
    time.sleep(2)
    
    print("直接从100%跳到0%...")
    send_position_update(base_url, exchange, symbol3, "update_pos", side, 0)  # 直接0%
    time.sleep(1)
    
    position5 = get_position_data(base_url, exchange, symbol3, side)
    print_position_summary(position5, "测试5结果(跳跃)")
    
    # 测试6: 实时更新验证
    print("\n⏰ 测试6: 实时更新验证")
    print("-" * 40)
    
    symbol4 = "SOLUSDT"
    side = "LONG"
    
    print("开仓并达到95%...")
    send_position_update(base_url, exchange, symbol4, "open_pos", side, 0)
    time.sleep(1)
    send_position_update(base_url, exchange, symbol4, "update_pos", side, 0.96)
    
    print("等待5秒观察持仓时间实时增长...")
    for i in range(5):
        time.sleep(1)
        position = get_position_data(base_url, exchange, symbol4, side)
        if position:
            print(f"  第{i+1}秒: 持仓时间 = {position.get('holding_duration', 0)} 秒")
    
    # 最终汇总
    print("\n📊 最终汇总")
    print("=" * 80)
    
    all_positions = requests.get(f"{base_url}/api/positions").json()
    for exch, symbols in all_positions.items():
        for sym, sides in symbols.items():
            for side_name, pos in sides.items():
                print(f"{exch}/{sym}/{side_name}: 开仓{pos.get('opening_duration',0)}s, "
                      f"持仓{pos.get('holding_duration',0)}s, 清仓{pos.get('closing_duration',0)}s")
    
    print("\n✅ 全面测试完成！")
    print("🌐 请访问 http://172.31.43.182:5000 查看前端显示")

if __name__ == "__main__":
    test_comprehensive()
