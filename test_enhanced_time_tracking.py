#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
from datetime import datetime

def test_enhanced_time_tracking():
    """测试增强的时间记录功能"""
    base_url = "http://172.31.43.182:5000"
    api_url = f"{base_url}/api/position"
    
    exchange = "test_exchange"
    symbol = "TESTUSDT"
    side = "LONG"
    
    print("🧪 测试增强的时间记录功能")
    print("=" * 60)
    print("📋 测试场景:")
    print("  1. 开仓耗时：从开始开仓到仓位达到95%")
    print("  2. 持仓时间：仓位在95%以上的时间")
    print("  3. 清仓耗时：从仓位降到95%以下到降到5%")
    print("  4. 实时更新：每秒钟自动更新时间")
    print("=" * 60)
    
    # 1. 发送开仓开始信号
    print("1️⃣ 发送开仓开始信号 (action=open_pos)")
    open_data = {
        "exchange": exchange,
        "symbol": symbol,
        "action": "open_pos",
        "position": {
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "open_price": "50000",
            "open_quantity": "1.0",
            "open_avg_price": "50000",
            "current_quantity": "0",
            "position_side": side
        }
    }
    
    response = requests.post(api_url, json=open_data)
    if response.status_code == 200:
        print("✅ 开仓开始信号发送成功")
    else:
        print(f"❌ 发送失败: {response.text}")
        return
    
    time.sleep(2)
    
    # 2. 逐步建仓到95%
    print("2️⃣ 逐步建仓到95%")
    for i in range(1, 11):
        current_quantity = 0.1 * i
        progress = current_quantity / 1.0 * 100
        
        update_data = {
            "exchange": exchange,
            "symbol": symbol,
            "action": "update_pos",
            "position": {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "open_price": "50000",
                "open_quantity": "1.0",
                "open_avg_price": "50000",
                "current_quantity": str(current_quantity),
                "position_side": side
            }
        }
        
        response = requests.post(api_url, json=update_data)
        if response.status_code == 200:
            print(f"📊 建仓进度: {progress:.1f}% ({current_quantity}/1.0)")
            if progress >= 95:
                print("🎯 达到95%，开始计算持仓时间")
        else:
            print(f"❌ 更新失败: {response.text}")
        
        time.sleep(1)
    
    # 3. 持仓一段时间（保持在95%以上）
    print("3️⃣ 持仓阶段（保持在95%以上）...")
    print("💡 在此期间，持仓时间会每秒钟自动增加")
    for i in range(5):
        print(f"   持仓中... {i+1}/5 秒")
        time.sleep(1)
    
    # 4. 开始减仓（从100%降到90%，触发清仓计时）
    print("4️⃣ 开始减仓（从95%以上降到95%以下）")
    for i in range(10, 8, -1):  # 从100%降到90%
        current_quantity = 0.1 * i
        progress = current_quantity / 1.0 * 100
        
        update_data = {
            "exchange": exchange,
            "symbol": symbol,
            "action": "update_pos",
            "position": {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "open_price": "50000",
                "open_quantity": "1.0",
                "open_avg_price": "50000",
                "current_quantity": str(current_quantity),
                "position_side": side
            }
        }
        
        response = requests.post(api_url, json=update_data)
        if response.status_code == 200:
            print(f"📉 减仓进度: {progress:.1f}% ({current_quantity}/1.0)")
            if progress < 95:
                print("🎯 降到95%以下，开始计算清仓耗时")
        else:
            print(f"❌ 更新失败: {response.text}")
        
        time.sleep(1)
    
    # 5. 继续减仓到5%
    print("5️⃣ 继续减仓到5%")
    print("💡 在此期间，清仓耗时会每秒钟自动增加")
    for i in range(8, 0, -1):  # 从80%降到10%
        current_quantity = 0.1 * i
        progress = current_quantity / 1.0 * 100
        
        update_data = {
            "exchange": exchange,
            "symbol": symbol,
            "action": "update_pos",
            "position": {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "open_price": "50000",
                "open_quantity": "1.0",
                "open_avg_price": "50000",
                "current_quantity": str(current_quantity),
                "position_side": side
            }
        }
        
        response = requests.post(api_url, json=update_data)
        if response.status_code == 200:
            print(f"📉 减仓进度: {progress:.1f}% ({current_quantity}/1.0)")
            if progress <= 5:
                print("🎯 降到5%，清仓完成")
                break
        else:
            print(f"❌ 更新失败: {response.text}")
        
        time.sleep(1)
    
    # 6. 等待几秒钟观察实时更新
    print("6️⃣ 等待几秒钟观察实时更新...")
    for i in range(3):
        print(f"   观察中... {i+1}/3 秒")
        time.sleep(1)
    
    # 7. 获取最终数据
    print("7️⃣ 获取最终数据")
    try:
        response = requests.get(f"{base_url}/api/positions")
        if response.status_code == 200:
            data = response.json()
            if exchange in data and symbol in data[exchange] and side in data[exchange][symbol]:
                position = data[exchange][symbol][side]
                print("\n📊 最终时间记录结果:")
                print("=" * 50)
                print(f"📅 开仓开始时间: {position.get('open_start_time', 'N/A')}")
                print(f"📈 达到95%时间: {position.get('position_95_time', 'N/A')}")
                print(f"📉 降到95%以下时间: {position.get('position_below_95_time', 'N/A')}")
                print(f"📉 降到5%时间: {position.get('position_5_time', 'N/A')}")
                print("=" * 50)
                print(f"⏱️  开仓耗时: {position.get('opening_duration', 0)} 秒")
                print(f"⏰ 持仓时间: {position.get('holding_duration', 0)} 秒")
                print(f"⏲️  清仓耗时: {position.get('closing_duration', 0)} 秒")
                print("=" * 50)
                print(f"📊 当前仓位: {position.get('current_quantity', 0)}/{position.get('open_quantity', 0)} ({position.get('progress', 0):.1f}%)")
                print(f"💰 浮动盈亏: {position.get('pnl', 0):.2f} USDT")
            else:
                print("❌ 未找到持仓数据")
        else:
            print(f"❌ 获取数据失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    print("\n✅ 增强时间记录功能测试完成")
    print(f"🌐 请访问 {base_url} 查看前端实时显示")
    print("💡 前端页面会每秒钟自动更新时间显示")

if __name__ == "__main__":
    test_enhanced_time_tracking()
