# 多交易所持仓可视化项目

目的是可以在一个页面上看到多个交易所的同一个币种的持仓情况，包括：
1. 开始开仓时间
2. 开始开仓价格
3. 开始开仓数量
4. 开始开仓方向
5. 开始开仓类型
6. 开始开仓杠杆
7. 开始开仓保证金
8. 开仓完成时间
9. 开仓耗时
10. 平均开仓价格
11. 浮动盈亏
12. 当前持仓

# 实现方法
## HTTP API
使用python构建一个web server，提供一个http post接口，每个交易所的交易程序用json上报，
json格式：
```
{
    "exchange": "binance",
    "symbol": "BTCUSDT",
    "action": "update_pos", // initial_pos, update_pos
    "position": {
        "time": "2020-01-01 00:00:00",
        "open_price": "",
        "open_quantity": "",
        "open_avg_price": "",
        "current_quantity": "",
        "position_side": "LONG", // short, long
    }
}
```

## html 实时展示
我希望有一个html页面，可以实时展示接收的json的结果：
一个交易所 两个status bar，一个bar代表long仓位，一个bar代表short仓位，bar的长度代表仓位的大小，bar的颜色代表盈亏情况，
bar的进度表示当前持仓占目标持仓的比例，
每个bar 都要显示两个时间，一个时间是从开仓到开仓完成的时间，另一个时间持仓时间，从开仓完成开始算
