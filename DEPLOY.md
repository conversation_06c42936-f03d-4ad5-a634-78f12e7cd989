# 部署说明文档

## 🚀 一键部署脚本

本项目提供了三个部署和管理脚本，让你可以轻松地将多交易所持仓可视化项目部署到远程服务器。

## 📋 脚本说明

### 1. `smart_deploy.sh` - 智能部署（强烈推荐）

最智能的部署方式，自动检测环境并选择最佳配置。

```bash
./smart_deploy.sh live-analyze 5000
```

**特点：**
- 🧠 自动检测远程环境（Python、Conda、权限等）
- 📁 智能选择部署目录（避免权限问题）
- 🔄 自动停止旧服务并启动新服务
- 🛠️ 生成专用管理脚本
- ✅ 完整的部署验证
- 🎯 支持SSH配置别名

### 2. `quick_deploy.sh` - 快速部署

简单的部署方式，适合快速测试和开发环境。

```bash
./quick_deploy.sh user@*************
```

**特点：**
- 一条命令完成部署
- 自动停止旧服务
- 自动安装依赖
- 自动启动新服务
- 部署到用户home目录

### 3. `deploy.sh` - 完整部署

功能最全面的部署脚本，适合生产环境。

```bash
./deploy.sh user@************* 5000
```

**特点：**
- 完整的错误检查
- 创建管理脚本
- 支持自定义端口
- 部署到用户home目录下的deploy目录
- 生成详细的管理命令

### 4. `manage.sh` - 服务管理

用于管理已部署的服务。

```bash
./manage.sh user@************* <action>
```

**可用操作：**
- `start` - 启动服务
- `stop` - 停止服务
- `restart` - 重启服务
- `status` - 查看状态
- `logs` - 查看日志
- `test` - 运行测试

## 🛠️ 使用示例

### 首次部署

```bash
# 智能部署（强烈推荐，支持SSH别名）
./smart_deploy.sh live-analyze 5000

# 快速部署（推荐新手）
./quick_deploy.sh user@*************

# 或者完整部署（推荐生产环境）
./deploy.sh user@************* 5000
```

### 更新部署

```bash
# 智能更新（推荐，会自动处理所有情况）
./smart_deploy.sh live-analyze 5000

# 快速更新（会自动停止旧服务）
./quick_deploy.sh user@*************

# 或者使用管理脚本重启
./manage.sh user@************* restart
```

### 服务管理

```bash
# 查看服务状态
./manage.sh user@************* status

# 查看实时日志
./manage.sh user@************* logs

# 停止服务
./manage.sh user@************* stop

# 启动服务
./manage.sh user@************* start

# 运行测试
./manage.sh user@************* test
```

## 📋 前置要求

### 本地环境
- Linux/macOS 系统
- 已配置SSH密钥认证到目标服务器
- 项目文件完整（app.py, requirements.txt, templates/, static/ 等）

### 目标服务器
- Linux 系统
- Python 3.7+ 环境
- 已安装 conda 或 pip
- SSH 访问权限

## 🔧 SSH 配置

为了方便使用，建议配置SSH密钥认证：

```bash
# 生成SSH密钥（如果还没有）
ssh-keygen -t rsa -b 4096

# 复制公钥到服务器
ssh-copy-id user@*************

# 测试连接
ssh user@************* "echo 'SSH连接成功'"
```

## 🌐 访问服务

部署成功后，可以通过以下方式访问：

- **Web界面**: `http://服务器IP:5000`
- **API接口**: `http://服务器IP:5000/api/position`

## 📊 监控和日志

### 查看服务状态
```bash
./manage.sh user@************* status
```

### 查看实时日志
```bash
./manage.sh user@************* logs
```

### 手动查看日志
```bash
ssh user@************* "tail -f /tmp/caokong_vz/logs/service.log"
```

## 🔍 故障排除

### 1. SSH连接失败
```bash
# 检查SSH连接
ssh -v user@*************

# 检查SSH密钥
ssh-add -l
```

### 2. 服务启动失败
```bash
# 查看详细日志
./manage.sh user@************* logs

# 手动启动调试
ssh user@************* "cd /tmp/caokong_vz && python app.py"
```

### 3. 端口被占用
```bash
# 检查端口占用
ssh user@************* "netstat -tlnp | grep :5000"

# 强制停止占用进程
ssh user@************* "pkill -f 'python.*app.py'"
```

### 4. 依赖安装失败
```bash
# 手动安装依赖
ssh user@************* "cd /tmp/caokong_vz && pip install -r requirements.txt"
```

## 🔄 更新流程

当你修改了代码需要更新服务器时：

1. **快速更新**（推荐）：
   ```bash
   ./quick_deploy.sh user@*************
   ```

2. **手动更新**：
   ```bash
   # 停止服务
   ./manage.sh user@************* stop

   # 上传新文件
   scp app.py user@*************:/tmp/caokong_vz/

   # 启动服务
   ./manage.sh user@************* start
   ```

## 📝 注意事项

1. **权限问题**：确保SSH用户有足够权限创建目录和运行Python
2. **防火墙**：确保服务器5000端口对外开放
3. **Python环境**：脚本会自动尝试激活conda环境
4. **数据备份**：日志文件会保存在 `logs/` 目录下
5. **多次部署**：重复部署会自动覆盖旧代码并重启服务

## 🎯 最佳实践

1. **开发环境**：使用 `quick_deploy.sh` 快速迭代
2. **生产环境**：使用 `deploy.sh` 完整部署
3. **定期监控**：使用 `manage.sh status` 检查服务状态
4. **日志管理**：定期清理或轮转日志文件
5. **备份配置**：保存重要的配置和数据
