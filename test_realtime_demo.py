#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
from datetime import datetime

def test_realtime_demo():
    """演示实时更新功能"""
    base_url = "http://172.31.43.182:5000"
    api_url = f"{base_url}/api/position"
    
    exchange = "demo_exchange"
    symbol = "BTCUSDT"
    side = "LONG"
    
    print("🎬 演示实时时间更新功能")
    print("=" * 50)
    print("💡 请在浏览器中打开 http://172.31.43.182:5000 观察实时更新")
    print("=" * 50)
    
    # 1. 开始开仓
    print("1️⃣ 开始开仓...")
    open_data = {
        "exchange": exchange,
        "symbol": symbol,
        "action": "open_pos",
        "position": {
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "open_price": "65000",
            "open_quantity": "2.0",
            "open_avg_price": "65000",
            "current_quantity": "0",
            "position_side": side
        }
    }
    
    response = requests.post(api_url, json=open_data)
    if response.status_code == 200:
        print("✅ 开仓开始")
    
    # 2. 快速建仓到95%
    print("2️⃣ 快速建仓到95%...")
    for i in range(1, 20):
        current_quantity = 0.1 * i
        if current_quantity > 2.0:
            current_quantity = 2.0
        progress = current_quantity / 2.0 * 100
        
        update_data = {
            "exchange": exchange,
            "symbol": symbol,
            "action": "update_pos",
            "position": {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "open_price": "65000",
                "open_quantity": "2.0",
                "open_avg_price": "65000",
                "current_quantity": str(current_quantity),
                "position_side": side
            }
        }
        
        response = requests.post(api_url, json=update_data)
        if response.status_code == 200:
            print(f"📊 建仓: {progress:.1f}%")
            if progress >= 95:
                print("🎯 达到95%，开始持仓")
                break
        
        time.sleep(0.5)
    
    # 3. 持仓阶段 - 观察实时更新
    print("3️⃣ 持仓阶段 - 观察持仓时间实时增长...")
    print("💡 持仓时间会每秒钟自动增加，请在浏览器中观察")
    for i in range(10):
        print(f"   持仓中... {i+1}/10 秒")
        time.sleep(1)
    
    # 4. 开始清仓
    print("4️⃣ 开始清仓...")
    for i in range(19, 0, -1):
        current_quantity = 0.1 * i
        if current_quantity > 2.0:
            continue
        progress = current_quantity / 2.0 * 100
        
        update_data = {
            "exchange": exchange,
            "symbol": symbol,
            "action": "update_pos",
            "position": {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "open_price": "65000",
                "open_quantity": "2.0",
                "open_avg_price": "65000",
                "current_quantity": str(current_quantity),
                "position_side": side
            }
        }
        
        response = requests.post(api_url, json=update_data)
        if response.status_code == 200:
            print(f"📉 清仓: {progress:.1f}%")
            if progress < 95 and progress > 5:
                print("🎯 开始清仓计时")
            elif progress <= 5:
                print("🎯 清仓完成")
                break
        
        time.sleep(0.8)
    
    # 5. 观察最终结果
    print("5️⃣ 清仓完成，观察最终结果...")
    for i in range(5):
        print(f"   观察中... {i+1}/5 秒")
        time.sleep(1)
    
    print("\n✅ 演示完成！")
    print("🌐 请在浏览器中查看完整的时间记录")
    print("💡 所有时间都会实时更新，即使没有新的数据上报")

if __name__ == "__main__":
    test_realtime_demo()
