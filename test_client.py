#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import random
from datetime import datetime, timedelta

class PositionTestClient:
    def __init__(self, base_url="http://18.183.131.174:5000"):
        self.base_url = base_url
        self.api_url = f"{base_url}/api/position"

    def send_position_data(self, exchange, symbol, position_side,
                          open_price, open_quantity, current_quantity,
                          open_avg_price=None, action="update_pos"):
        """发送持仓数据"""
        if open_avg_price is None:
            open_avg_price = open_price

        data = {
            "exchange": exchange,
            "symbol": symbol,
            "action": action,
            "position": {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "open_price": str(open_price),
                "open_quantity": str(open_quantity),
                "open_avg_price": str(open_avg_price),
                "current_quantity": str(current_quantity),
                "position_side": position_side,
                "current_price": str(open_price + random.uniform(-100, 100))  # 模拟当前价格
            }
        }

        try:
            response = requests.post(self.api_url, json=data)
            if response.status_code == 200:
                print(f"✅ 成功发送: {exchange} {symbol} {position_side} - {current_quantity}/{open_quantity}")
                return True
            else:
                print(f"❌ 发送失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始基本功能测试...")
    client = PositionTestClient()

    # 测试数据
    test_cases = [
        ("binance", "BTCUSDT", "LONG", 50000, 1.0, 0.8),
        ("binance", "BTCUSDT", "SHORT", 51000, 0.5, 0.3),
        ("okx", "BTCUSDT", "LONG", 49800, 2.0, 1.5),
        ("bybit", "ETHUSDT", "LONG", 3000, 5.0, 4.2),
        ("bybit", "ETHUSDT", "SHORT", 3100, 3.0, 2.1),
    ]

    for exchange, symbol, side, price, open_qty, current_qty in test_cases:
        client.send_position_data(exchange, symbol, side, price, open_qty, current_qty)
        time.sleep(0.5)  # 避免请求过快

    print("✅ 基本功能测试完成")

def test_real_time_updates():
    """测试实时更新功能"""
    print("🔄 开始实时更新测试...")
    client = PositionTestClient()

    # 模拟持仓变化
    exchanges = ["binance", "okx", "bybit"]
    symbols = ["BTCUSDT", "ETHUSDT"]
    sides = ["LONG", "SHORT"]

    for i in range(10):
        exchange = random.choice(exchanges)
        symbol = random.choice(symbols)
        side = random.choice(sides)

        base_price = 50000 if symbol == "BTCUSDT" else 3000
        open_price = base_price + random.uniform(-1000, 1000)
        open_quantity = random.uniform(0.1, 5.0)
        current_quantity = open_quantity * random.uniform(0.3, 1.0)

        client.send_position_data(exchange, symbol, side, open_price,
                                open_quantity, current_quantity)

        print(f"📊 第 {i+1}/10 次更新完成")
        time.sleep(2)

    print("✅ 实时更新测试完成")

def test_progressive_position_building():
    """测试逐步建仓过程"""
    print("📈 开始逐步建仓测试...")
    client = PositionTestClient()

    # 模拟逐步建仓
    exchange = "binance"
    symbol = "BTCUSDT"
    side = "LONG"
    open_price = 50000
    target_quantity = 2.0

    # 1. 发送开仓开始信号
    print("🚀 发送开仓开始信号...")
    client.send_position_data(exchange, symbol, side, open_price,
                            target_quantity, 0, open_price, "open_pos")
    time.sleep(1)

    # 2. 逐步建仓
    steps = 10
    for i in range(1, steps + 1):
        current_quantity = (target_quantity / steps) * i
        avg_price = open_price + random.uniform(-50, 50)  # 模拟平均价格变化

        client.send_position_data(exchange, symbol, side, open_price,
                                target_quantity, current_quantity, avg_price, "update_pos")

        progress = (current_quantity / target_quantity) * 100
        print(f"🏗️  建仓进度: {progress:.1f}% ({current_quantity:.2f}/{target_quantity})")
        time.sleep(1)

    # 3. 模拟持仓一段时间后减仓
    print("⏰ 模拟持仓过程...")
    time.sleep(2)

    # 减仓到10%
    for i in range(9, 0, -1):
        current_quantity = (target_quantity / 10) * i
        avg_price = open_price + random.uniform(-100, 100)

        client.send_position_data(exchange, symbol, side, open_price,
                                target_quantity, current_quantity, avg_price, "update_pos")

        progress = (current_quantity / target_quantity) * 100
        print(f"📉 减仓进度: {progress:.1f}% ({current_quantity:.2f}/{target_quantity})")
        time.sleep(1)

    print("✅ 逐步建仓测试完成")

def test_pnl_scenarios():
    """测试盈亏场景"""
    print("💰 开始盈亏场景测试...")
    client = PositionTestClient()

    # 盈利场景
    print("📈 测试盈利场景...")
    client.send_position_data("binance", "BTCUSDT", "LONG", 50000, 1.0, 1.0, 50000)
    time.sleep(1)

    # 亏损场景
    print("📉 测试亏损场景...")
    client.send_position_data("okx", "BTCUSDT", "LONG", 52000, 1.0, 1.0, 52000)
    time.sleep(1)

    # 做空盈利
    print("📈 测试做空盈利...")
    client.send_position_data("bybit", "ETHUSDT", "SHORT", 3200, 2.0, 2.0, 3200)
    time.sleep(1)

    # 做空亏损
    print("📉 测试做空亏损...")
    client.send_position_data("bybit", "ETHUSDT", "SHORT", 2800, 2.0, 2.0, 2800)

    print("✅ 盈亏场景测试完成")

def main():
    """主测试函数"""
    print("🚀 多交易所持仓可视化测试客户端")
    print("=" * 50)

    # 检查服务器连接
    try:
        response = requests.get("http://localhost:5000")
        if response.status_code == 200:
            print("✅ 服务器连接正常")
        else:
            print("❌ 服务器响应异常")
            return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保服务器已启动 (python app.py)")
        return

    print("\n选择测试模式:")
    print("1. 基本功能测试")
    print("2. 实时更新测试")
    print("3. 逐步建仓测试")
    print("4. 盈亏场景测试")
    print("5. 全部测试")

    try:
        choice = input("\n请输入选择 (1-5): ").strip()

        if choice == "1":
            test_basic_functionality()
        elif choice == "2":
            test_real_time_updates()
        elif choice == "3":
            test_progressive_position_building()
        elif choice == "4":
            test_pnl_scenarios()
        elif choice == "5":
            test_basic_functionality()
            print("\n" + "="*30 + "\n")
            test_real_time_updates()
            print("\n" + "="*30 + "\n")
            test_progressive_position_building()
            print("\n" + "="*30 + "\n")
            test_pnl_scenarios()
        else:
            print("❌ 无效选择")

    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
