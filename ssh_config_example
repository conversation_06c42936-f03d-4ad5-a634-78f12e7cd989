# SSH配置示例文件
# 将此文件内容添加到 ~/.ssh/config 中，方便管理多个服务器

# 生产服务器
Host prod-server
    HostName *************
    User ubuntu
    Port 22
    IdentityFile ~/.ssh/id_rsa
    ServerAliveInterval 60
    ServerAliveCountMax 3

# 测试服务器
Host test-server
    HostName *************
    User ubuntu
    Port 22
    IdentityFile ~/.ssh/id_rsa
    ServerAliveInterval 60
    ServerAliveCountMax 3

# 开发服务器
Host dev-server
    HostName *************
    User ubuntu
    Port 22
    IdentityFile ~/.ssh/id_rsa
    ServerAliveInterval 60
    ServerAliveCountMax 3

# 使用示例:
# ./quick_deploy.sh prod-server
# ./manage.sh test-server status
# ./deploy.sh dev-server 5001
