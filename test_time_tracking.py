#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
from datetime import datetime

def test_time_tracking():
    """测试时间记录功能"""
    base_url = "http://172.31.43.182:5000"
    api_url = f"{base_url}/api/position"

    exchange = "test_exchange"
    symbol = "TESTUSDT"
    side = "LONG"

    print("🧪 测试时间记录功能")
    print("=" * 50)

    # 1. 发送开仓开始信号
    print("1️⃣ 发送开仓开始信号 (action=open_pos)")
    open_data = {
        "exchange": exchange,
        "symbol": symbol,
        "action": "open_pos",
        "position": {
            "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "open_price": "50000",
            "open_quantity": "1.0",
            "open_avg_price": "50000",
            "current_quantity": "0",
            "position_side": side
        }
    }

    response = requests.post(api_url, json=open_data)
    if response.status_code == 200:
        print("✅ 开仓开始信号发送成功")
    else:
        print(f"❌ 发送失败: {response.text}")
        return

    time.sleep(2)

    # 2. 逐步建仓到95%
    print("2️⃣ 逐步建仓到95%")
    for i in range(1, 11):
        current_quantity = 0.1 * i
        progress = current_quantity / 1.0 * 100

        update_data = {
            "exchange": exchange,
            "symbol": symbol,
            "action": "update_pos",
            "position": {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "open_price": "50000",
                "open_quantity": "1.0",
                "open_avg_price": "50000",
                "current_quantity": str(current_quantity),
                "position_side": side
            }
        }

        response = requests.post(api_url, json=update_data)
        if response.status_code == 200:
            print(f"📊 建仓进度: {progress:.1f}% ({current_quantity}/1.0)")
            if progress >= 95:
                print("🎯 达到95%，开仓耗时开始计算")
        else:
            print(f"❌ 更新失败: {response.text}")

        time.sleep(1)

    # 3. 持仓一段时间
    print("3️⃣ 持仓阶段...")
    time.sleep(3)

    # 4. 逐步减仓到5%
    print("4️⃣ 逐步减仓到5%")
    for i in range(9, 0, -1):
        current_quantity = 0.1 * i
        progress = current_quantity / 1.0 * 100

        update_data = {
            "exchange": exchange,
            "symbol": symbol,
            "action": "update_pos",
            "position": {
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "open_price": "50000",
                "open_quantity": "1.0",
                "open_avg_price": "50000",
                "current_quantity": str(current_quantity),
                "position_side": side
            }
        }

        response = requests.post(api_url, json=update_data)
        if response.status_code == 200:
            print(f"📉 减仓进度: {progress:.1f}% ({current_quantity}/1.0)")
            if progress <= 5:
                print("🎯 降到5%，持仓时间计算完成")
        else:
            print(f"❌ 更新失败: {response.text}")

        time.sleep(1)

    # 5. 获取最终数据
    print("5️⃣ 获取最终数据")
    try:
        response = requests.get(f"{base_url}/api/positions")
        if response.status_code == 200:
            data = response.json()
            if exchange in data and symbol in data[exchange] and side in data[exchange][symbol]:
                position = data[exchange][symbol][side]
                print("\n📊 时间记录结果:")
                print(f"开仓开始时间: {position.get('open_start_time', 'N/A')}")
                print(f"达到95%时间: {position.get('position_95_time', 'N/A')}")
                print(f"降到95%以下时间: {position.get('position_below_95_time', 'N/A')}")
                print(f"降到5%时间: {position.get('position_5_time', 'N/A')}")
                print(f"开仓耗时: {position.get('opening_duration', 0)} 秒")
                print(f"持仓时间: {position.get('holding_duration', 0)} 秒")
                print(f"清仓耗时: {position.get('closing_duration', 0)} 秒")
            else:
                print("❌ 未找到持仓数据")
        else:
            print(f"❌ 获取数据失败: {response.text}")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

    print("\n✅ 时间记录功能测试完成")
    print(f"🌐 请访问 {base_url} 查看前端显示")

if __name__ == "__main__":
    test_time_tracking()
