#!/bin/bash

# 远程服务管理脚本
# 使用方法: ./manage.sh <ssh_host> <action>
# action: start|stop|restart|status|logs|test

set -e

if [ $# -lt 2 ]; then
    echo "使用方法: $0 <ssh_host> <action>"
    echo ""
    echo "可用操作:"
    echo "  start   - 启动服务"
    echo "  stop    - 停止服务"
    echo "  restart - 重启服务"
    echo "  status  - 查看状态"
    echo "  logs    - 查看日志"
    echo "  test    - 运行测试"
    echo ""
    echo "例如: $0 user@************* status"
    exit 1
fi

SSH_HOST=$1
ACTION=$2

# 获取远程用户的home目录，与deploy.sh保持一致
REMOTE_USER_HOME=$(ssh "$SSH_HOST" "echo \$HOME")
# 尝试多个可能的部署目录
POSSIBLE_DIRS=(
    "$REMOTE_USER_HOME/deploy/caokong_vz"
    "$REMOTE_USER_HOME/tmp/caokong_vz"
    "$REMOTE_USER_HOME/caokong_vz"
)

PROJECT_DIR=""
for dir in "${POSSIBLE_DIRS[@]}"; do
    if ssh "$SSH_HOST" "[ -d \"$dir\" ]"; then
        PROJECT_DIR="$dir"
        break
    fi
done

if [ -z "$PROJECT_DIR" ]; then
    echo "❌ 未找到部署目录，请先运行 ./deploy.sh $SSH_HOST"
    exit 1
fi

# 动态检测端口
get_port() {
    ssh "$SSH_HOST" "cd '$PROJECT_DIR' && grep -o 'port=[0-9]*' app.py | cut -d= -f2 | head -1" 2>/dev/null || echo "5000"
}

PORT=$(get_port)

case $ACTION in
    "start")
        echo "🚀 启动服务..."
        ssh "$SSH_HOST" << EOF
cd $PROJECT_DIR
# 激活虚拟环境
if [ -d venv ]; then
    source venv/bin/activate
else
    source ~/miniconda3/bin/activate 2>/dev/null || source ~/anaconda3/bin/activate 2>/dev/null || true
fi
nohup python app.py > logs/service.log 2>&1 &
echo \$! > app.pid
sleep 2
if kill -0 \$(cat app.pid) 2>/dev/null; then
    echo "✅ 服务启动成功，PID: \$(cat app.pid)"
    echo "🌐 访问地址: http://\$(hostname -I | awk '{print \$1}'):$PORT"
else
    echo "❌ 服务启动失败"
    exit 1
fi
EOF
        ;;

    "stop")
        echo "🛑 停止服务..."
        ssh "$SSH_HOST" << EOF
cd $PROJECT_DIR
if [ -f app.pid ]; then
    PID=\$(cat app.pid)
    if kill -0 \$PID 2>/dev/null; then
        kill \$PID
        rm app.pid
        echo "✅ 服务已停止"
    else
        echo "⚠️  服务未运行"
        rm -f app.pid
    fi
else
    echo "⚠️  未找到PID文件，尝试强制停止..."
    pkill -f "python.*app.py" || echo "没有找到运行中的服务"
fi
EOF
        ;;

    "restart")
        echo "🔄 重启服务..."
        "$0" "$SSH_HOST" stop
        sleep 2
        "$0" "$SSH_HOST" start
        ;;

    "status")
        echo "📊 检查服务状态..."
        ssh "$SSH_HOST" << EOF
cd $PROJECT_DIR
PORT_NUM=\$(grep -o 'port=[0-9]*' app.py | cut -d= -f2 | head -1)
if [ -f app.pid ]; then
    PID=\$(cat app.pid)
    if kill -0 \$PID 2>/dev/null; then
        echo "✅ 服务正在运行"
        echo "   PID: \$PID"
        echo "   内存使用: \$(ps -p \$PID -o rss= | awk '{print \$1/1024 \"MB\"}' 2>/dev/null || echo '未知')"
        echo "   运行时间: \$(ps -p \$PID -o etime= | tr -d ' ' 2>/dev/null || echo '未知')"
        echo "   访问地址: http://\$(hostname -I | awk '{print \$1}'):\$PORT_NUM"

        # 检查端口是否监听
        if ss -tlnp 2>/dev/null | grep -q ":\$PORT_NUM" || netstat -tlnp 2>/dev/null | grep -q ":\$PORT_NUM.*LISTEN"; then
            echo "   端口状态: ✅ \$PORT_NUM端口正在监听"
        else
            echo "   端口状态: ⚠️  \$PORT_NUM端口未监听"
        fi
    else
        echo "❌ 服务未运行 (PID文件存在但进程不存在)"
        rm -f app.pid
    fi
else
    echo "❌ 服务未运行"
fi
EOF
        ;;

    "logs")
        echo "📋 查看服务日志..."
        ssh "$SSH_HOST" "cd '$PROJECT_DIR' && tail -f logs/service.log"
        ;;

    "test")
        echo "🧪 运行测试..."
        ssh "$SSH_HOST" << EOF
cd $PROJECT_DIR
# 激活虚拟环境
if [ -d venv ]; then
    source venv/bin/activate
else
    source ~/miniconda3/bin/activate 2>/dev/null || source ~/anaconda3/bin/activate 2>/dev/null || true
fi
echo "1" | python test_client.py
EOF
        ;;

    *)
        echo "❌ 未知操作: $ACTION"
        echo "可用操作: start|stop|restart|status|logs|test"
        exit 1
        ;;
esac
