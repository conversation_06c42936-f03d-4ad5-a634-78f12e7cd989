// WebSocket连接和数据管理
class PositionVisualizer {
    constructor() {
        this.socket = null;
        this.positions = {};
        this.filters = {
            exchange: '',
            symbol: ''
        };
        this.init();
    }

    init() {
        this.connectWebSocket();
        this.setupFilters();
        this.updateConnectionStatus('连接中...');
    }

    connectWebSocket() {
        this.socket = io();

        this.socket.on('connect', () => {
            console.log('WebSocket连接成功');
            this.updateConnectionStatus('已连接');
        });

        this.socket.on('disconnect', () => {
            console.log('WebSocket连接断开');
            this.updateConnectionStatus('连接断开');
        });

        this.socket.on('initial_data', (data) => {
            console.log('收到初始数据:', data);
            this.positions = data;
            this.updateExchangeFilter();
            this.renderAllPositions();
        });

        this.socket.on('position_update', (update) => {
            console.log('收到持仓更新:', update);
            this.updatePosition(update);
            this.updateLastUpdateTime();
        });

        // 监听数据清除事件
        this.socket.on('positions_cleared', (data) => {
            console.log('收到数据清除事件:', data.message);
            this.positions = {};
            this.renderAllPositions();
            this.updateExchangeFilter();
            this.showMessage('📢 ' + data.message, 'info');
        });
    }

    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connection-status');
        statusElement.textContent = status;

        if (status === '已连接') {
            statusElement.className = 'status-connected';
        } else {
            statusElement.className = 'status-disconnected';
        }
    }

    updateLastUpdateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN');
        document.getElementById('last-update').textContent = `最后更新: ${timeString}`;
    }

    setupFilters() {
        const exchangeFilter = document.getElementById('exchange-filter');
        const symbolFilter = document.getElementById('symbol-filter');
        const clearSymbolBtn = document.getElementById('clear-symbol-filter');
        const resetAllBtn = document.getElementById('reset-all-btn');

        // 交易所筛选
        exchangeFilter.addEventListener('change', (e) => {
            this.filters.exchange = e.target.value;
            this.renderAllPositions();
        });

        // 品种筛选 - 实时输入
        symbolFilter.addEventListener('input', (e) => {
            this.filters.symbol = e.target.value.toUpperCase();
            this.renderAllPositions();
        });

        // 清除品种筛选
        clearSymbolBtn.addEventListener('click', () => {
            symbolFilter.value = '';
            this.filters.symbol = '';
            this.renderAllPositions();
        });

        // 重置所有数据
        resetAllBtn.addEventListener('click', () => {
            this.resetAllPositions();
        });
    }

    updateExchangeFilter() {
        const exchangeFilter = document.getElementById('exchange-filter');
        const currentValue = exchangeFilter.value;

        // 清空现有选项（保留"全部交易所"）
        exchangeFilter.innerHTML = '<option value="">全部交易所</option>';

        // 添加所有交易所选项
        const exchanges = Object.keys(this.positions);
        exchanges.forEach(exchange => {
            const option = document.createElement('option');
            option.value = exchange;
            option.textContent = exchange;
            if (exchange === currentValue) {
                option.selected = true;
            }
            exchangeFilter.appendChild(option);
        });
    }

    shouldShowPosition(exchange, symbol) {
        // 检查交易所筛选
        if (this.filters.exchange && exchange !== this.filters.exchange) {
            return false;
        }

        // 检查品种筛选（模糊匹配）
        if (this.filters.symbol && !symbol.toUpperCase().includes(this.filters.symbol)) {
            return false;
        }

        return true;
    }

    updatePosition(update) {
        const { exchange, symbol, position_side, data } = update;

        // 更新内存数据
        if (!this.positions[exchange]) {
            this.positions[exchange] = {};
        }
        if (!this.positions[exchange][symbol]) {
            this.positions[exchange][symbol] = {};
        }
        this.positions[exchange][symbol][position_side] = data;

        // 更新交易所筛选选项
        this.updateExchangeFilter();

        // 重新渲染
        this.renderAllPositions();
    }

    renderAllPositions() {
        const container = document.getElementById('exchanges-container');

        if (Object.keys(this.positions).length === 0) {
            container.innerHTML = `
                <div class="no-data">
                    <p>等待交易所数据...</p>
                    <p class="hint">请向 <code>/api/position</code> 发送POST请求上报持仓数据</p>
                </div>
            `;
            return;
        }

        let html = '';
        let visibleCount = 0;

        for (const [exchange, symbols] of Object.entries(this.positions)) {
            for (const [symbol, positions] of Object.entries(symbols)) {
                // 应用筛选条件
                if (this.shouldShowPosition(exchange, symbol)) {
                    html += this.renderExchangeCard(exchange, symbol, positions);
                    visibleCount++;
                }
            }
        }

        // 如果没有符合筛选条件的数据
        if (visibleCount === 0) {
            const filterInfo = [];
            if (this.filters.exchange) filterInfo.push(`交易所: ${this.filters.exchange}`);
            if (this.filters.symbol) filterInfo.push(`品种: ${this.filters.symbol}`);

            container.innerHTML = `
                <div class="no-data">
                    <p>没有符合筛选条件的数据</p>
                    <p class="hint">当前筛选: ${filterInfo.join(', ') || '无'}</p>
                </div>
            `;
        } else {
            container.innerHTML = html;
        }
    }

    renderExchangeCard(exchange, symbol, positions) {
        const longPos = positions.LONG || null;
        const shortPos = positions.SHORT || null;

        return `
            <div class="exchange-card">
                <div class="exchange-header">
                    <div class="exchange-name">${exchange}</div>
                    <div class="symbol-info">${symbol}</div>
                </div>
                <div class="positions-container">
                    ${this.renderPositionBar('LONG', longPos)}
                    ${this.renderPositionBar('SHORT', shortPos)}
                </div>
            </div>
        `;
    }

    renderPositionBar(side, position) {
        const sideClass = side.toLowerCase();

        if (!position) {
            return `
                <div class="position-bar">
                    <div class="position-header">
                        <span class="position-side ${sideClass}">${side}</span>
                        <span class="position-info">无持仓</span>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar neutral" style="width: 0%">
                            无持仓
                        </div>
                    </div>
                </div>
            `;
        }

        const progress = Math.min(position.progress || 0, 100);
        const pnl = position.pnl || 0;
        const isProfit = position.is_profit || false;
        const colorClass = pnl === 0 ? 'neutral' : (isProfit ? 'profit' : 'loss');

        const currentQuantity = position.current_quantity || 0;
        const openQuantity = position.open_quantity || 0;
        const openAvgPrice = position.open_avg_price || 0;
        const openingDuration = this.formatDuration(position.opening_duration || 0);
        const holdingDuration = this.formatDuration(position.holding_duration || 0);
        const closingDuration = this.formatDuration(position.closing_duration || 0);
        const openTime = position.open_time || '';
        const openStartTime = position.open_start_time || '';
        const position95Time = position.position_95_time || '';
        const positionBelow95Time = position.position_below_95_time || '';
        const position5Time = position.position_5_time || '';

        return `
            <div class="position-bar">
                <div class="position-header">
                    <span class="position-side ${sideClass}">${side}</span>
                    <span class="position-info">
                        ${currentQuantity}/${openQuantity} (${progress.toFixed(1)}%)
                    </span>
                </div>
                <div class="progress-container">
                    <div class="progress-bar ${colorClass}" style="width: ${progress}%">
                        ${pnl >= 0 ? '+' : ''}${pnl.toFixed(2)} USDT
                    </div>
                </div>
                <div class="position-details">
                    <div class="detail-item">
                        <div class="detail-label">开仓价格</div>
                        <div class="detail-value">${parseFloat(openAvgPrice).toFixed(2)}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">开仓耗时</div>
                        <div class="detail-value">${openingDuration}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">持仓时间</div>
                        <div class="detail-value">${holdingDuration}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">清仓耗时</div>
                        <div class="detail-value">${closingDuration}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">浮动盈亏</div>
                        <div class="detail-value" style="color: ${isProfit ? '#e74c3c' : '#27ae60'}">
                            ${pnl >= 0 ? '+' : ''}${pnl.toFixed(2)}
                        </div>
                    </div>
                </div>
                ${this.renderTimeDetails(openStartTime, position95Time, positionBelow95Time, position5Time)}
            </div>
        `;
    }

    renderTimeDetails(openStartTime, position95Time, positionBelow95Time, position5Time) {
        if (!openStartTime && !position95Time && !positionBelow95Time && !position5Time) {
            return '';
        }

        return `
            <div class="time-details">
                <div class="time-details-header">时间节点</div>
                <div class="time-details-content">
                    ${openStartTime ? `<div class="time-item"><span class="time-label">开仓开始:</span> ${openStartTime}</div>` : ''}
                    ${position95Time ? `<div class="time-item"><span class="time-label">达到95%:</span> ${position95Time}</div>` : ''}
                    ${positionBelow95Time ? `<div class="time-item"><span class="time-label">降到95%以下:</span> ${positionBelow95Time}</div>` : ''}
                    ${position5Time ? `<div class="time-item"><span class="time-label">降到5%:</span> ${position5Time}</div>` : ''}
                </div>
            </div>
        `;
    }

    formatDuration(seconds) {
        if (seconds < 60) {
            return `${seconds}秒`;
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            return `${minutes}分钟`;
        } else if (seconds < 86400) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}小时${minutes}分钟`;
        } else {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            return `${days}天${hours}小时`;
        }
    }

    async resetAllPositions() {
        const resetBtn = document.getElementById('reset-all-btn');

        // 确认对话框
        if (!confirm('确定要清除所有仓位数据吗？此操作不可撤销。')) {
            return;
        }

        // 禁用按钮并显示加载状态
        resetBtn.disabled = true;
        resetBtn.textContent = '🔄 重置中...';

        try {
            const response = await fetch('/api/positions/clear', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                console.log('重置成功:', result.message);

                // 清空本地数据
                this.positions = {};

                // 重新渲染界面
                this.renderAllPositions();
                this.updateExchangeFilter();

                // 显示成功消息
                this.showMessage('✅ 所有仓位数据已清除', 'success');
            } else {
                throw new Error(`重置失败: ${response.status}`);
            }
        } catch (error) {
            console.error('重置数据时发生错误:', error);
            this.showMessage('❌ 重置失败，请检查网络连接', 'error');
        } finally {
            // 恢复按钮状态
            resetBtn.disabled = false;
            resetBtn.textContent = '🧹 重置所有数据';
        }
    }

    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = message;

        // 添加样式
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
        `;

        // 添加到页面
        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            messageDiv.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 300);
        }, 3000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new PositionVisualizer();
});
