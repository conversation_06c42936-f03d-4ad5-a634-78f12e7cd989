#!/bin/bash

# 脚本测试工具
# 用于测试 deploy.sh 和 manage.sh 的基本功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo "=========================================="
echo "  脚本测试工具"
echo "=========================================="

# 1. 检查脚本文件存在
print_info "1. 检查脚本文件..."
if [ -f "deploy.sh" ] && [ -f "manage.sh" ]; then
    print_success "脚本文件存在"
else
    print_error "缺少脚本文件"
    exit 1
fi

# 2. 检查脚本权限
print_info "2. 检查脚本权限..."
if [ -x "deploy.sh" ] && [ -x "manage.sh" ]; then
    print_success "脚本权限正确"
else
    print_warning "脚本权限不足，尝试修复..."
    chmod +x deploy.sh manage.sh
    print_success "权限已修复"
fi

# 3. 语法检查
print_info "3. 语法检查..."
if bash -n deploy.sh && bash -n manage.sh; then
    print_success "语法检查通过"
else
    print_error "语法检查失败"
    exit 1
fi

# 4. 帮助信息测试
print_info "4. 测试帮助信息..."
echo "--- deploy.sh 帮助信息 ---"
./deploy.sh 2>&1 | head -5
echo ""
echo "--- manage.sh 帮助信息 ---"
./manage.sh 2>&1 | head -10

# 5. 检查必要文件
print_info "5. 检查项目文件..."
required_files=("app.py" "requirements.txt" "templates/index.html" "static/style.css" "static/script.js" "test_client.py")
missing_files=()

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    print_success "所有必要文件存在"
else
    print_warning "缺少文件: ${missing_files[*]}"
fi

# 6. 检查 app.py 端口配置
print_info "6. 检查 app.py 端口配置..."
if grep -q "port=5000" app.py; then
    print_success "发现默认端口配置: 5000"
else
    print_warning "未找到标准端口配置"
fi

# 7. 模拟本地测试（不需要SSH）
print_info "7. 模拟本地部署测试..."
echo "测试命令示例:"
echo "  ./deploy.sh localhost 8080"
echo "  ./manage.sh localhost status"
echo "  ./manage.sh localhost start"
echo "  ./manage.sh localhost stop"

print_success "基本测试完成！"
echo ""
echo "=========================================="
echo "  测试结果总结"
echo "=========================================="
echo "✅ 脚本语法正确"
echo "✅ 权限配置正确"
echo "✅ 帮助信息正常"
echo "✅ 项目文件检查完成"
echo ""
echo "下一步测试建议:"
echo "1. 准备一个测试服务器"
echo "2. 配置SSH密钥认证"
echo "3. 运行: ./deploy.sh <your-server> 5000"
echo "4. 运行: ./manage.sh <your-server> status"
echo "=========================================="
